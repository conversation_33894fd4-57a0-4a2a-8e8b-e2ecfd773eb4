import { createRouter, createWebHistory } from 'vue-router'
import ThinkingTrainer from './components/ThinkingTrainer.vue'
import AnalysisPage from './components/AnalysisPage.vue'
import DeductiveAnalysisPage from './components/DeductiveAnalysisPage.vue'

const routes = [
  {
    path: '/',
    name: 'Home',
    component: ThinkingTrainer
  },
  {
    path: '/analysis',
    name: 'Analysis',
    component: AnalysisPage
  },
  {
    path: '/deductive-analysis',
    name: 'DeductiveAnalysis',
    component: DeductiveAnalysisPage
  }
]

const router = createRouter({
  // 使用Web History模式，配合Vercel的rewrites配置
  // 如果部署环境不支持SPA路由，可以改为 createWebHashHistory()
  history: createWebHistory(),
  routes
})

export default router
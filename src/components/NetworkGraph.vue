<script setup lang="ts">
import { ref, onMounted, watch, nextTick, shallowRef, computed } from 'vue'
import { type AnalysisNode, type NodeConnection, getAllThinkingModelDetails } from '../utils/systemicThinking'

interface Props {
  nodes: AnalysisNode[]
  connections: NodeConnection[]
  thinkingDepth: number
}

const props = defineProps<Props>()

const svgRef = ref<SVGElement>()
const selectedNode = shallowRef<AnalysisNode | null>(null)
const hoveredNode = shallowRef<AnalysisNode | null>(null)

const modelDetails = getAllThinkingModelDetails();

// 拖拽相关状态
const isDragging = ref(false)
const draggedNode = ref<AnalysisNode | null>(null)
const dragOffset = ref({ x: 0, y: 0 })

// 缩放和平移状态
const scale = ref(1)
const translate = ref({ x: 0, y: 0 })
const isPanning = ref(false)
const panStart = ref({ x: 0, y: 0 })

// 触摸事件状态
const touchState = ref({
  isTouch: false,
  touches: [] as Touch[],
  initialDistance: 0,
  initialScale: 1,
  initialTranslate: { x: 0, y: 0 },
  lastTouchTime: 0,
  tapCount: 0
})

// 移动端性能优化
const isMobile = ref(false)
const performanceMode = ref(false) // 性能模式，减少动画和效果

// 性能优化：使用 Map 缓存计算结果
const nodePositions = new Map<string, { x: number, y: number }>()
const connectionPaths = new Map<string, string>()

// 响应式视口尺寸
const viewportSize = ref({ width: 1200, height: 900 })

// 性能优化：计算属性缓存
const scalePercentage = computed(() => Math.round(scale.value * 100))

const nodeStrokeColor = computed(() => (node: AnalysisNode) => {
  const currentSelectedNode = selectedNode.value;
  if (currentSelectedNode && currentSelectedNode.id === node.id) {
    return '#1d4ed8';
  }
  return 'white';
});

const nodeStrokeWidth = computed(() => (node: AnalysisNode) => {
  const currentSelectedNode = selectedNode.value;
  if (currentSelectedNode && currentSelectedNode.id === node.id) {
    return 4;
  }
  return 3;
});

const nodeOpacity = computed(() => (node: AnalysisNode) => {
  if (!hoveredNode.value && !selectedNode.value) return 1;

  if (hoveredNode.value && hoveredNode.value.id !== node.id) {
    return 0.6; 
  }

  if (selectedNode.value && selectedNode.value.id !== node.id) {
    return 0.6;
  }

  return 1;
});

const getNodeColor = (node: AnalysisNode) => {
  if (node.type === 'initial') return '#64748b'; // Slate-500 for initial node
  if (node.type === 'positive') return '#22c55e'; // Green-500 for positive nodes
  if (node.type === 'negative') return '#ef4444'; // Red-500 for negative nodes

  const modelDetail = getAllThinkingModelDetails().find(m => m.id === node.modelSource);
  if (modelDetail && modelDetail.color) {
    return modelDetail.color;
  }
  switch (node.level) {
    case 0: return '#2563eb';
    case 1: return '#059669';
    case 2: return '#dc2626';
    case 3: return '#7c3aed';
    default: return '#ea580c';
  }
};

const getNodeSize = (level: number) => {
  const baseSize = 50
  const sizeReduction = Math.min(level * 8, 25)
  return Math.max(baseSize - sizeReduction, 20)
}

// 更新视口尺寸
const updateViewportSize = () => {
  if (svgRef.value) {
    const rect = svgRef.value.getBoundingClientRect()
    viewportSize.value = {
      width: Math.max(rect.width, 800),
      height: Math.max(rect.height, 600)
    }
  }
}

// 性能优化：使用 Web Workers 进行布局计算（模拟）
const layoutNodes = () => {
  if (!props.nodes.length) return

  const width = viewportSize.value.width
  const height = viewportSize.value.height
  const centerX = width / 2
  const centerY = height / 2

  // 清除缓存
  nodePositions.clear()
  connectionPaths.clear()

  // 按层级分组
  const nodesByLevel: { [key: number]: AnalysisNode[] } = {}
  props.nodes.forEach(node => {
    if (!nodesByLevel[node.level]) {
      nodesByLevel[node.level] = []
    }
    nodesByLevel[node.level].push(node)
  })

  // 为每个层级的节点分配位置
  Object.keys(nodesByLevel).forEach(levelStr => {
    const level = parseInt(levelStr)
    const nodesInLevel = nodesByLevel[level]
    const radius = level === 0 ? 0 : Math.min(120 + level * 140, Math.min(width, height) / 3)
    
    if (level === 0) {
      // 核心节点在中心
      const node = nodesInLevel[0]
      const pos = node.x !== undefined && node.y !== undefined ? { x: node.x, y: node.y } : { x: centerX, y: centerY }
      nodePositions.set(node.id, pos)
      node.x = pos.x
      node.y = pos.y
    } else {
      // 其他节点围绕中心分布
      const angleStep = (2 * Math.PI) / nodesInLevel.length
      nodesInLevel.forEach((node, index) => {
        const pos = node.x !== undefined && node.y !== undefined ? 
          { x: node.x, y: node.y } : 
          (() => {
            const angle = index * angleStep - Math.PI / 2
            return { x: centerX + radius * Math.cos(angle), y: centerY + radius * Math.sin(angle) }
          })()
        nodePositions.set(node.id, pos)
        node.x = pos.x
        node.y = pos.y
      })
    }
  })

  // 预计算连接路径
  props.connections.forEach(connection => {
    const path = getConnectionPath(connection)
    connectionPaths.set(`${connection.from}-${connection.to}`, path)
  })
}

// 触摸事件处理函数
const getTouchDistance = (touch1: Touch, touch2: Touch) => {
  const dx = touch1.clientX - touch2.clientX
  const dy = touch1.clientY - touch2.clientY
  return Math.sqrt(dx * dx + dy * dy)
}

const getTouchCenter = (touch1: Touch, touch2: Touch) => {
  return {
    x: (touch1.clientX + touch2.clientX) / 2,
    y: (touch1.clientY + touch2.clientY) / 2
  }
}

const handleTouchStart = (event: TouchEvent, node?: AnalysisNode) => {
  event.preventDefault()
  touchState.value.isTouch = true
  touchState.value.touches = Array.from(event.touches)

  const now = Date.now()
  if (now - touchState.value.lastTouchTime < 300) {
    touchState.value.tapCount++
  } else {
    touchState.value.tapCount = 1
  }
  touchState.value.lastTouchTime = now

  if (event.touches.length === 1) {
    // 单指操作
    const touch = event.touches[0]
    if (node) {
      // 开始拖拽节点
      isDragging.value = true
      draggedNode.value = node
      const rect = svgRef.value?.getBoundingClientRect()
      if (rect) {
        dragOffset.value = {
          x: (touch.clientX - rect.left) / scale.value - translate.value.x - node.x!,
          y: (touch.clientY - rect.top) / scale.value - translate.value.y - node.y!
        }
      }
    } else {
      // 开始平移画布
      isPanning.value = true
      panStart.value = { x: touch.clientX, y: touch.clientY }
    }
  } else if (event.touches.length === 2) {
    // 双指缩放
    isDragging.value = false
    isPanning.value = false
    draggedNode.value = null

    touchState.value.initialDistance = getTouchDistance(event.touches[0], event.touches[1])
    touchState.value.initialScale = scale.value
    touchState.value.initialTranslate = { ...translate.value }
  }
}

// 鼠标事件处理 - 性能优化
const handleMouseDown = (event: MouseEvent, node?: AnalysisNode) => {
  event.preventDefault()

  if (node) {
    // 开始拖拽节点
    isDragging.value = true
    draggedNode.value = node
    const rect = svgRef.value?.getBoundingClientRect()
    if (rect) {
      dragOffset.value = {
        x: (event.clientX - rect.left) / scale.value - translate.value.x - node.x!,
        y: (event.clientY - rect.top) / scale.value - translate.value.y - node.y!
      }
    }
  } else {
    // 开始平移画布
    isPanning.value = true
    panStart.value = { x: event.clientX, y: event.clientY }
  }
}

const handleTouchMove = (event: TouchEvent) => {
  if (!touchState.value.isTouch) return
  event.preventDefault()

  if (animationFrameId) {
    cancelAnimationFrame(animationFrameId)
  }

  animationFrameId = requestAnimationFrame(() => {
    if (event.touches.length === 1) {
      // 单指操作
      const touch = event.touches[0]
      if (isDragging.value && draggedNode.value) {
        // 拖拽节点
        const rect = svgRef.value?.getBoundingClientRect()
        if (rect) {
          const newX = (touch.clientX - rect.left) / scale.value - translate.value.x - dragOffset.value.x
          const newY = (touch.clientY - rect.top) / scale.value - translate.value.y - dragOffset.value.y

          draggedNode.value.x = newX
          draggedNode.value.y = newY
          nodePositions.set(draggedNode.value.id, { x: newX, y: newY })

          // 重新计算相关连接
          props.connections.forEach(connection => {
            if (connection.from === draggedNode.value!.id || connection.to === draggedNode.value!.id) {
              const path = getConnectionPath(connection)
              connectionPaths.set(`${connection.from}-${connection.to}`, path)
            }
          })
        }
      } else if (isPanning.value) {
        // 平移画布
        const deltaX = touch.clientX - panStart.value.x
        const deltaY = touch.clientY - panStart.value.y

        translate.value = {
          x: translate.value.x + deltaX / scale.value,
          y: translate.value.y + deltaY / scale.value
        }

        panStart.value = { x: touch.clientX, y: touch.clientY }
      }
    } else if (event.touches.length === 2) {
      // 双指缩放
      const currentDistance = getTouchDistance(event.touches[0], event.touches[1])
      const center = getTouchCenter(event.touches[0], event.touches[1])
      const rect = svgRef.value?.getBoundingClientRect()

      if (rect && touchState.value.initialDistance > 0) {
        const scaleRatio = currentDistance / touchState.value.initialDistance
        const newScale = Math.max(0.1, Math.min(3, touchState.value.initialScale * scaleRatio))

        // 以触摸中心为缩放中心
        const centerX = center.x - rect.left
        const centerY = center.y - rect.top

        const scaleDelta = newScale / scale.value
        translate.value = {
          x: centerX - (centerX - translate.value.x) * scaleDelta,
          y: centerY - (centerY - translate.value.y) * scaleDelta
        }

        scale.value = newScale
      }
    }
  })
}

const handleTouchEnd = (event: TouchEvent) => {
  event.preventDefault()

  if (event.touches.length === 0) {
    // 所有手指离开
    touchState.value.isTouch = false
    isDragging.value = false
    draggedNode.value = null
    isPanning.value = false

    if (animationFrameId) {
      cancelAnimationFrame(animationFrameId)
      animationFrameId = null
    }
  } else if (event.touches.length === 1) {
    // 从双指变为单指
    touchState.value.initialDistance = 0
    const touch = event.touches[0]
    panStart.value = { x: touch.clientX, y: touch.clientY }
    isPanning.value = true
  }
}

// 性能优化：使用 requestAnimationFrame 进行平滑更新
let animationFrameId: number | null = null

const handleMouseMove = (event: MouseEvent) => {
  if (animationFrameId) {
    cancelAnimationFrame(animationFrameId)
  }

  animationFrameId = requestAnimationFrame(() => {
    if (isDragging.value && draggedNode.value) {
      // 拖拽节点
      const rect = svgRef.value?.getBoundingClientRect()
      if (rect) {
        const newX = (event.clientX - rect.left) / scale.value - translate.value.x - dragOffset.value.x
        const newY = (event.clientY - rect.top) / scale.value - translate.value.y - dragOffset.value.y

        draggedNode.value.x = newX
        draggedNode.value.y = newY
        nodePositions.set(draggedNode.value.id, { x: newX, y: newY })

        // 重新计算相关连接
        props.connections.forEach(connection => {
          if (connection.from === draggedNode.value!.id || connection.to === draggedNode.value!.id) {
            const path = getConnectionPath(connection)
            connectionPaths.set(`${connection.from}-${connection.to}`, path)
          }
        })
      }
    } else if (isPanning.value) {
      // 平移画布
      const deltaX = event.clientX - panStart.value.x
      const deltaY = event.clientY - panStart.value.y

      translate.value = {
        x: translate.value.x + deltaX / scale.value,
        y: translate.value.y + deltaY / scale.value
      }

      panStart.value = { x: event.clientX, y: event.clientY }
    }
  })
}

const handleMouseUp = () => {
  isDragging.value = false
  draggedNode.value = null
  isPanning.value = false
  
  if (animationFrameId) {
    cancelAnimationFrame(animationFrameId)
    animationFrameId = null
  }
}

// 缩放处理 - 性能优化
const handleWheel = (event: WheelEvent) => {
  event.preventDefault()
  
  const rect = svgRef.value?.getBoundingClientRect()
  if (!rect) return
  
  const mouseX = event.clientX - rect.left
  const mouseY = event.clientY - rect.top
  
  const delta = event.deltaY > 0 ? 0.9 : 1.1
  const newScale = Math.max(0.1, Math.min(3, scale.value * delta))
  
  // 以鼠标位置为中心缩放
  const scaleRatio = newScale / scale.value
  translate.value = {
    x: mouseX - (mouseX - translate.value.x) * scaleRatio,
    y: mouseY - (mouseY - translate.value.y) * scaleRatio
  }
  
  scale.value = newScale
}

const selectNode = (node: AnalysisNode) => {
  selectedNode.value = selectedNode.value?.id === node.id ? null : node
}

const hoverNode = (node: AnalysisNode | null) => {
  hoveredNode.value = node
}

const getConnectionPath = (connection: NodeConnection) => {
  const cacheKey = `${connection.from}-${connection.to}`
  if (connectionPaths.has(cacheKey)) {
    return connectionPaths.get(cacheKey)!
  }

  const fromPos = nodePositions.get(connection.from)
  const toPos = nodePositions.get(connection.to)
  
  if (!fromPos || !toPos) return ''
  
  // 使用二次贝塞尔曲线创建更平滑的连接
  const midX = (fromPos.x + toPos.x) / 2
  const midY = (fromPos.y + toPos.y) / 2
  const controlOffset = 50
  
  const path = `M ${fromPos.x} ${fromPos.y} Q ${midX} ${midY - controlOffset} ${toPos.x} ${toPos.y}`
  connectionPaths.set(cacheKey, path)
  return path
}

const getConnectionOpacity = (connection: NodeConnection) => {
  if (!hoveredNode.value && !selectedNode.value) return 0.6
  
  const targetNode = hoveredNode.value || selectedNode.value
  if (connection.from === targetNode?.id || connection.to === targetNode?.id) {
    return 1
  }
  
  return 0.2
}

const getConnectionWidth = (connection: NodeConnection) => {
  const baseWidth = connection.type === 'strong' ? 3 : 2
  const strengthMultiplier = connection.strength || 1
  return baseWidth * strengthMultiplier
}

const selectedNodeModelName = computed(() => {
  if (selectedNode.value) {
    const modelDetail = getAllThinkingModelDetails().find(m => m.id === selectedNode.value!.modelSource);
    return modelDetail?.name || selectedNode.value!.modelSource;
  }
  return '';
});

// 节点散开功能
const spreadNodes = () => {
  if (!props.nodes.length) return

  // 检测节点重叠
  const overlappingNodes = findOverlappingNodes()

  if (overlappingNodes.length === 0) {
    // 如果没有重叠，使用力导向算法重新分布所有节点
    applyForceDirectedLayout()
  } else {
    // 只散开重叠的节点
    spreadOverlappingNodes(overlappingNodes)
  }
}

const findOverlappingNodes = () => {
  const overlapping: AnalysisNode[] = []
  const nodeRadius = 30 // 节点半径

  for (let i = 0; i < props.nodes.length; i++) {
    for (let j = i + 1; j < props.nodes.length; j++) {
      const node1 = props.nodes[i]
      const node2 = props.nodes[j]

      if (node1.x !== undefined && node1.y !== undefined &&
          node2.x !== undefined && node2.y !== undefined) {
        const distance = Math.sqrt(
          Math.pow(node1.x - node2.x, 2) + Math.pow(node1.y - node2.y, 2)
        )

        if (distance < nodeRadius * 2) {
          if (!overlapping.includes(node1)) overlapping.push(node1)
          if (!overlapping.includes(node2)) overlapping.push(node2)
        }
      }
    }
  }

  return overlapping
}

const spreadOverlappingNodes = (overlappingNodes: AnalysisNode[]) => {
  const centerX = viewportSize.value.width / 2
  const centerY = viewportSize.value.height / 2
  const spreadRadius = 150

  overlappingNodes.forEach((node, index) => {
    const angle = (index / overlappingNodes.length) * 2 * Math.PI
    const radius = spreadRadius + (Math.random() - 0.5) * 50

    const newX = centerX + radius * Math.cos(angle)
    const newY = centerY + radius * Math.sin(angle)

    node.x = newX
    node.y = newY
    nodePositions.set(node.id, { x: newX, y: newY })
  })

  // 重新计算连接路径
  updateConnectionPaths()
}

const applyForceDirectedLayout = () => {
  const width = viewportSize.value.width
  const height = viewportSize.value.height

  // 简单的力导向算法
  const iterations = 50
  const repulsionStrength = 1000
  const attractionStrength = 0.1
  const damping = 0.9

  // 为每个节点初始化速度
  const velocities = new Map<string, { vx: number, vy: number }>()
  props.nodes.forEach(node => {
    velocities.set(node.id, { vx: 0, vy: 0 })
  })

  for (let iter = 0; iter < iterations; iter++) {
    // 计算排斥力
    for (let i = 0; i < props.nodes.length; i++) {
      for (let j = i + 1; j < props.nodes.length; j++) {
        const node1 = props.nodes[i]
        const node2 = props.nodes[j]

        if (node1.x !== undefined && node1.y !== undefined &&
            node2.x !== undefined && node2.y !== undefined) {
          const dx = node1.x - node2.x
          const dy = node1.y - node2.y
          const distance = Math.sqrt(dx * dx + dy * dy) || 1

          const force = repulsionStrength / (distance * distance)
          const fx = (dx / distance) * force
          const fy = (dy / distance) * force

          const vel1 = velocities.get(node1.id)!
          const vel2 = velocities.get(node2.id)!

          vel1.vx += fx
          vel1.vy += fy
          vel2.vx -= fx
          vel2.vy -= fy
        }
      }
    }

    // 计算吸引力（基于连接）
    props.connections.forEach(connection => {
      const node1 = props.nodes.find(n => n.id === connection.from)
      const node2 = props.nodes.find(n => n.id === connection.to)

      if (node1 && node2 && node1.x !== undefined && node1.y !== undefined &&
          node2.x !== undefined && node2.y !== undefined) {
        const dx = node2.x - node1.x
        const dy = node2.y - node1.y
        const distance = Math.sqrt(dx * dx + dy * dy) || 1

        const force = attractionStrength * distance
        const fx = (dx / distance) * force
        const fy = (dy / distance) * force

        const vel1 = velocities.get(node1.id)!
        const vel2 = velocities.get(node2.id)!

        vel1.vx += fx
        vel1.vy += fy
        vel2.vx -= fx
        vel2.vy -= fy
      }
    })

    // 应用速度和阻尼
    props.nodes.forEach(node => {
      const vel = velocities.get(node.id)!

      if (node.x !== undefined && node.y !== undefined) {
        node.x += vel.vx
        node.y += vel.vy

        // 边界约束
        node.x = Math.max(50, Math.min(width - 50, node.x))
        node.y = Math.max(50, Math.min(height - 50, node.y))

        nodePositions.set(node.id, { x: node.x, y: node.y })
      }

      vel.vx *= damping
      vel.vy *= damping
    })
  }

  updateConnectionPaths()
}

const updateConnectionPaths = () => {
  connectionPaths.clear()
  props.connections.forEach(connection => {
    const path = getConnectionPath(connection)
    connectionPaths.set(`${connection.from}-${connection.to}`, path)
  })
}

// 重置视图
const resetView = () => {
  scale.value = 1
  translate.value = { x: 0, y: 0 }
}

// 使用 requestAnimationFrame 优化布局计算
let layoutRAF: number | null = null

const scheduleLayout = () => {
  if (layoutRAF) {
    cancelAnimationFrame(layoutRAF)
  }
  layoutRAF = requestAnimationFrame(() => {
    updateViewportSize()
    layoutNodes()
    layoutRAF = null
  })
}

watch(() => [props.nodes, props.connections], () => {
  nextTick(() => {
    scheduleLayout()
  })
}, { immediate: true })

// 检测移动端设备
const detectMobile = () => {
  const userAgent = navigator.userAgent.toLowerCase()
  const mobileKeywords = ['mobile', 'android', 'iphone', 'ipad', 'ipod', 'blackberry', 'windows phone']
  isMobile.value = mobileKeywords.some(keyword => userAgent.includes(keyword)) ||
                   window.innerWidth <= 768 ||
                   'ontouchstart' in window

  // 移动端自动启用性能模式
  if (isMobile.value) {
    performanceMode.value = true
  }
}

// 性能优化的节点渲染
const shouldRenderNode = (node: AnalysisNode) => {
  if (!performanceMode.value) return true

  // 在性能模式下，只渲染可见区域内的节点
  const margin = 100
  const nodeX = (node.x || 0) * scale.value + translate.value.x
  const nodeY = (node.y || 0) * scale.value + translate.value.y

  return nodeX >= -margin &&
         nodeX <= viewportSize.value.width + margin &&
         nodeY >= -margin &&
         nodeY <= viewportSize.value.height + margin
}

// 性能优化的连接渲染
const shouldRenderConnection = (connection: NodeConnection) => {
  if (!performanceMode.value) return true

  const fromNode = props.nodes.find(n => n.id === connection.from)
  const toNode = props.nodes.find(n => n.id === connection.to)

  if (!fromNode || !toNode) return false

  return shouldRenderNode(fromNode) || shouldRenderNode(toNode)
}

// 节流函数用于性能优化
const throttle = <T extends (...args: any[]) => void>(func: T, delay: number): T => {
  let timeoutId: ReturnType<typeof setTimeout> | null = null
  let lastExecTime = 0

  return ((...args: any[]) => {
    const currentTime = Date.now()

    if (currentTime - lastExecTime > delay) {
      func(...args)
      lastExecTime = currentTime
    } else {
      if (timeoutId) clearTimeout(timeoutId)
      timeoutId = setTimeout(() => {
        func(...args)
        lastExecTime = Date.now()
      }, delay - (currentTime - lastExecTime))
    }
  }) as T
}

// 性能优化的布局调度
const throttledScheduleLayout = throttle(scheduleLayout, isMobile.value ? 100 : 50)

onMounted(() => {
  detectMobile()
  scheduleLayout()

  // 添加全局事件监听
  document.addEventListener('mousemove', handleMouseMove, { passive: true })
  document.addEventListener('mouseup', handleMouseUp, { passive: true })
  document.addEventListener('touchmove', handleTouchMove, { passive: false })
  document.addEventListener('touchend', handleTouchEnd, { passive: true })
  document.addEventListener('touchcancel', handleTouchEnd, { passive: true })

  // 监听窗口大小变化，使用节流优化
  window.addEventListener('resize', throttledScheduleLayout)

  // 监听设备方向变化（移动端）
  if (isMobile.value) {
    window.addEventListener('orientationchange', () => {
      setTimeout(throttledScheduleLayout, 100) // 延迟执行以确保尺寸更新
    })
  }
})
</script>

<template>
  <div class="relative w-full h-full bg-white dark:bg-slate-800 rounded-lg border border-slate-200 dark:border-slate-700 overflow-hidden">
    <!-- 控制按钮 -->
    <div class="absolute top-2 sm:top-4 right-2 sm:right-4 z-10 flex flex-col space-y-1 sm:space-y-2">
      <button
        @click="resetView"
        class="p-1.5 sm:p-2 bg-white dark:bg-slate-700 rounded-lg shadow-md hover:shadow-lg transition-shadow text-slate-600 dark:text-slate-300 hover:text-slate-800 dark:hover:text-slate-100"
        title="重置视图"
      >
        <svg class="h-3 w-3 sm:h-4 sm:w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
        </svg>
      </button>
      <button
        @click="spreadNodes"
        class="p-1.5 sm:p-2 bg-white dark:bg-slate-700 rounded-lg shadow-md hover:shadow-lg transition-shadow text-slate-600 dark:text-slate-300 hover:text-slate-800 dark:hover:text-slate-100"
        title="散开节点"
      >
        <svg class="h-3 w-3 sm:h-4 sm:w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4" />
        </svg>
      </button>
      <button
        v-if="!isMobile"
        @click="performanceMode = !performanceMode"
        :class="[
          'p-1.5 sm:p-2 rounded-lg shadow-md hover:shadow-lg transition-shadow text-slate-600 dark:text-slate-300 hover:text-slate-800 dark:hover:text-slate-100',
          performanceMode ? 'bg-green-100 dark:bg-green-900' : 'bg-white dark:bg-slate-700'
        ]"
        :title="performanceMode ? '关闭性能模式' : '开启性能模式'"
      >
        <svg class="h-3 w-3 sm:h-4 sm:w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
        </svg>
      </button>
      <div class="text-xs text-slate-500 dark:text-slate-400 bg-white dark:bg-slate-700 px-1.5 sm:px-2 py-0.5 sm:py-1 rounded shadow">
        缩放: {{ scalePercentage }}%
      </div>
    </div>

    <svg
      ref="svgRef"
      class="w-full h-full cursor-grab active:cursor-grabbing"
      :viewBox="`0 0 ${viewportSize.width} ${viewportSize.height}`"
      @mousedown="handleMouseDown"
      @wheel="handleWheel"
      @touchstart="handleTouchStart"
      @touchmove="handleTouchMove"
      @touchend="handleTouchEnd"
      @touchcancel="handleTouchEnd"
    >
      <g :transform="`translate(${translate.x}, ${translate.y}) scale(${scale})`">
        <!-- 连接线 -->
        <g class="connections">
          <path
            v-for="connection in connections"
            v-show="shouldRenderConnection(connection)"
            :key="`${connection.from}-${connection.to}`"
            :d="getConnectionPath(connection)"
            :stroke="connection.type === 'strong' ? '#2563eb' : '#64748b'"
            :stroke-width="getConnectionWidth(connection)"
            :stroke-opacity="getConnectionOpacity(connection)"
            fill="none"
            stroke-dasharray="5,5"
            :class="performanceMode ? 'transition-none' : 'transition-all duration-300'"
          />
        </g>

        <!-- 节点 -->
        <g class="nodes">
          <g
            v-for="node in nodes"
            v-show="shouldRenderNode(node)"
            :key="node.id"
            :transform="`translate(${node.x}, ${node.y})`"
            class="cursor-pointer"
            :class="performanceMode ? 'transition-none' : 'transition-all duration-300'"
            @click="selectNode(node)"
            @mouseenter="hoverNode(node)"
            @mouseleave="hoverNode(null)"
            @mousedown="(e) => handleMouseDown(e, node)"
            @touchstart="(e) => handleTouchStart(e, node)"
          >
            <!-- 节点圆圈 -->
            <circle
              :r="getNodeSize(node.level)"
              :fill="getNodeColor(node)"
              :stroke="nodeStrokeColor(node)"
              :stroke-width="nodeStrokeWidth(node)"
              :opacity="nodeOpacity(node)"
              class="transition-all duration-300"
            />
            
            <!-- 节点文本 -->
            <text
              :font-size="Math.max(12 - node.level, 9)"
              text-anchor="middle"
              dy="0.35em"
              fill="white"
              font-weight="600"
              class="pointer-events-none select-none"
            >
              {{ node.title.length > 10 ? node.title.substring(0, 10) + '...' : node.title }}
            </text>
            
            <!-- 层级指示器 -->
            <circle
              v-if="node.level > 0"
              :r="4"
              :cx="getNodeSize(node.level) - 8"
              :cy="-getNodeSize(node.level) + 8"
              :fill="getNodeColor(node)"
              stroke="white"
              stroke-width="2"
            />
            <text
              v-if="node.level > 0"
              :x="getNodeSize(node.level) - 8"
              :y="-getNodeSize(node.level) + 8"
              font-size="8"
              text-anchor="middle"
              dy="0.25em"
              fill="white"
              font-weight="bold"
              class="pointer-events-none select-none"
            >
              {{ node.level }}
            </text>
          </g>
        </g>
      </g>
    </svg>

    <!-- 节点详情面板 -->
    <div
      v-if="selectedNode"
      class="absolute top-2 sm:top-4 left-2 sm:left-4 bg-white dark:bg-slate-800 p-3 sm:p-4 rounded-lg shadow-lg border border-slate-200 dark:border-slate-700 max-w-xs z-10"
    >
      <div class="flex items-start justify-between mb-2">
        <h4 class="font-semibold text-sm sm:text-base text-slate-800 dark:text-slate-200 pr-2">{{ selectedNode.title }}</h4>
        <button
          @click="selectedNode = null"
          class="text-slate-400 dark:text-slate-500 hover:text-slate-600 dark:hover:text-slate-300 flex-shrink-0"
        >
          <svg class="h-3 w-3 sm:h-4 sm:w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>
      <p class="text-xs sm:text-sm text-slate-600 dark:text-slate-400 mb-1">类型: {{ selectedNodeModelName }}</p>
      <p class="text-xs sm:text-sm text-slate-600 dark:text-slate-400 mb-3">{{ selectedNode.description }}</p>
      
      <!-- 洞察 -->
      <div v-if="selectedNode.insights?.length" class="mb-3">
        <h5 class="text-xs font-medium text-slate-700 dark:text-slate-300 mb-1">关键洞察</h5>
        <div class="space-y-1">
          <div v-for="insight in selectedNode.insights" :key="insight" class="text-xs text-slate-600 dark:text-slate-400 bg-blue-50/80 dark:bg-blue-900/20 p-1 rounded">
            {{ insight }}
          </div>
        </div>
      </div>
      
      <div class="flex items-center justify-between text-xs text-slate-500 dark:text-slate-400">
        <span>层级: {{ selectedNode.level }}</span>
        <span>影响度: {{ selectedNode.impact }}</span>
      </div>
    </div>

    <!-- 图例 -->
    <div class="absolute bottom-2 sm:bottom-4 right-2 sm:right-4 bg-white dark:bg-slate-800 p-2 sm:p-3 rounded-lg shadow-lg border border-slate-200 dark:border-slate-700">
      <h5 class="text-xs font-semibold text-slate-700 dark:text-slate-300 mb-2">图例</h5>
      <div class="space-y-1">
        <div v-for="model in modelDetails" :key="model.id" class="flex items-center text-xs">
          <div class="w-2 h-2 sm:w-3 sm:h-3 rounded-full mr-1 sm:mr-2" :style="{ backgroundColor: model.color }"></div>
          <span class="text-slate-600 dark:text-slate-400 truncate">{{ model.name }}</span>
        </div>
      </div>
      <div class="mt-2 pt-2 border-t border-slate-200 dark:border-slate-600 text-xs text-slate-500 dark:text-slate-400 space-y-0.5">
        <div>💡 可拖拽节点</div>
        <div>🔍 滚轮缩放</div>
        <div>👆 拖拽平移</div>
      </div>
    </div>
  </div>
</template>
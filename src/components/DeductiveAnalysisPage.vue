<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { useRoute } from 'vue-router'
import DeductiveTreeGraph from './DeductiveTreeGraph.vue'
import ThemeToggle from './ThemeToggle.vue'
import { LLMDeductiveAnalyzer } from '../utils/deductiveThinking'
import { AnalysisNode, NodeConnection } from '../utils/systemicThinking'

const route = useRoute()
const topic = ref('')
const nodes = ref<AnalysisNode[]>([])
const edges = ref<NodeConnection[]>([])
const isLoading = ref(true)
const error = ref<string | null>(null)
const isFromCache = ref(false)

// 生成本地存储的键
const getStorageKey = (topic: string, thinkingPrompt: string) => {
  const key = `deductive_analysis_${topic}_${thinkingPrompt}`.replace(/[^a-zA-Z0-9_]/g, '_')
  return key.substring(0, 100) // 限制键长度
}

// 从本地存储加载结果
const loadFromStorage = (topic: string, thinkingPrompt: string) => {
  try {
    const key = getStorageKey(topic, thinkingPrompt)
    const stored = localStorage.getItem(key)
    if (stored) {
      const data = JSON.parse(stored)
      // 检查数据是否过期（7天）
      const now = Date.now()
      const maxAge = 7 * 24 * 60 * 60 * 1000 // 7天
      if (now - data.timestamp < maxAge) {
        return data.result
      } else {
        // 清除过期数据
        localStorage.removeItem(key)
      }
    }
  } catch (e) {
    console.warn('Failed to load from storage:', e)
  }
  return null
}

// 保存结果到本地存储
const saveToStorage = (topic: string, thinkingPrompt: string, result: any) => {
  try {
    const key = getStorageKey(topic, thinkingPrompt)
    const data = {
      timestamp: Date.now(),
      result: result
    }
    localStorage.setItem(key, JSON.stringify(data))
    console.log('DeductiveAnalysisPage: Saved to storage with key:', key)
  } catch (e) {
    console.warn('Failed to save to storage:', e)
  }
}

// 清除缓存
const clearCache = (topic: string, thinkingPrompt: string) => {
  try {
    const key = getStorageKey(topic, thinkingPrompt)
    localStorage.removeItem(key)
    console.log('DeductiveAnalysisPage: Cache cleared for key:', key)
  } catch (e) {
    console.warn('Failed to clear cache:', e)
  }
}

// 重新生成分析（强制不使用缓存）
const regenerateAnalysis = async () => {
  if (topic.value) {
    const thinkingPrompt = (route.query.thinkingPrompt as string) || ''
    clearCache(topic.value, thinkingPrompt)
    await generateDeductiveAnalysis(topic.value, thinkingPrompt, true)
  }
}

const generateDeductiveAnalysis = async (initialTopic: string, thinkingPrompt: string, forceRegenerate = false) => {
  isLoading.value = true
  error.value = null
  nodes.value = []
  edges.value = []
  isFromCache.value = false

  try {
    // 首先尝试从本地存储加载（除非强制重新生成）
    if (!forceRegenerate) {
      const cachedResult = loadFromStorage(initialTopic, thinkingPrompt)
      if (cachedResult) {
        console.log('DeductiveAnalysisPage: Loading from cache')
        isFromCache.value = true

        // 使用缓存的结果
        const result = cachedResult

      // Add initial node
      const initialNode: AnalysisNode = {
        id: 'initial-0',
        title: result.initialTopic,
        description: '初始思考提示',
        level: 0,
        impact: 'medium',
        modelSource: 'initial',
        type: 'initial'
      }
      nodes.value.push(initialNode)

      // Add positive branch nodes and connections
      nodes.value.push(...result.positiveBranch.nodes)
      edges.value.push(...result.positiveBranch.connections)
      if (result.positiveBranch.nodes.length > 0) {
        edges.value.push({
          from: initialNode.id,
          to: result.positiveBranch.nodes[0].id,
          type: 'strong',
          description: '积极方向',
          strength: 1
        })
      }

      // Add negative branch nodes and connections
      nodes.value.push(...result.negativeBranch.nodes)
      edges.value.push(...result.negativeBranch.connections)
      if (result.negativeBranch.nodes.length > 0) {
        edges.value.push({
          from: initialNode.id,
          to: result.negativeBranch.nodes[0].id,
          type: 'strong',
          description: '负面方向',
          strength: 1
        })
      }

      console.log('DeductiveAnalysisPage: Loaded from cache - nodes:', nodes.value.length, 'edges:', edges.value.length)
      return
    }
    }

    // 如果没有缓存，则生成新的分析
    console.log('DeductiveAnalysisPage: Generating new analysis')
    const analyzer = new LLMDeductiveAnalyzer()
    const result = await analyzer.generateDeductiveAnalysis(initialTopic, thinkingPrompt)

    console.log('DeductiveAnalysisPage: Generated result:', result)
    console.log('DeductiveAnalysisPage: Positive branch nodes:', result.positiveBranch.nodes.length)
    console.log('DeductiveAnalysisPage: Negative branch nodes:', result.negativeBranch.nodes.length)

    // 保存到本地存储
    saveToStorage(initialTopic, thinkingPrompt, result)

    // Add initial node
    const initialNode: AnalysisNode = {
      id: 'initial-0',
      title: result.initialTopic,
      description: '初始思考提示',
      level: 0,
      impact: 'medium',
      modelSource: 'initial',
      type: 'initial'
    }
    nodes.value.push(initialNode)

    // Add positive branch nodes and connections
    nodes.value.push(...result.positiveBranch.nodes)
    edges.value.push(...result.positiveBranch.connections)
    if (result.positiveBranch.nodes.length > 0) {
      edges.value.push({
        from: initialNode.id,
        to: result.positiveBranch.nodes[0].id,
        type: 'strong',
        description: '积极方向',
        strength: 1
      })
    }

    // Add negative branch nodes and connections
    nodes.value.push(...result.negativeBranch.nodes)
    edges.value.push(...result.negativeBranch.connections)
    if (result.negativeBranch.nodes.length > 0) {
      edges.value.push({
        from: initialNode.id,
        to: result.negativeBranch.nodes[0].id,
        type: 'strong',
        description: '负面方向',
        strength: 1
      })
    }

    console.log('DeductiveAnalysisPage: Final nodes count:', nodes.value.length)
    console.log('DeductiveAnalysisPage: Final edges count:', edges.value.length)

  } catch (e: any) {
    console.error('Failed to generate deductive analysis:', e)
    error.value = e.message || '生成推演分析失败'
  } finally {
    isLoading.value = false
  }
}

onMounted(() => {
  if (route.query.topic) {
    topic.value = route.query.topic as string
    const thinkingPrompt = (route.query.thinkingPrompt as string) || '';
    console.log('DeductiveAnalysisPage: Initializing with topic', topic.value, 'and thinkingPrompt', thinkingPrompt);
    generateDeductiveAnalysis(topic.value, thinkingPrompt)
  } else {
    // 如果没有topic参数，显示错误信息
    error.value = '缺少分析主题参数'
    isLoading.value = false
  }
})

watch([nodes, edges], () => {
  console.log('DeductiveAnalysisPage: Nodes updated', nodes.value);
  console.log('DeductiveAnalysisPage: Edges updated', edges.value);
});
</script>

<template>
  <div class="min-h-screen bg-slate-50 dark:bg-slate-900">
    <div class="h-screen flex flex-col">
      <!-- 扁平化头部 -->
      <div class="flex items-center justify-between p-4 sm:p-6 border-b border-slate-200 dark:border-slate-700 bg-white dark:bg-slate-800">
        <div class="flex-1 min-w-0">
          <div class="flex items-center space-x-3">
            <div class="w-10 h-10 bg-blue-500 rounded flex items-center justify-center">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
              </svg>
            </div>
            <div>
              <h1 class="text-xl sm:text-2xl font-bold text-slate-800 dark:text-slate-200">推演分析</h1>
              <p class="text-sm sm:text-base text-slate-600 dark:text-slate-400 mt-1">
                <span class="font-medium">主题：</span>{{ topic }}
              </p>
            </div>
          </div>
        </div>

        <!-- 状态指示器和操作按钮 -->
        <div class="flex items-center space-x-3">
          <div v-if="isLoading" class="flex items-center space-x-2 text-blue-600 dark:text-blue-400">
            <div class="w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
            <span class="text-sm font-medium hidden sm:inline">分析中</span>
          </div>
          <div v-else-if="error" class="flex items-center space-x-2 text-red-600 dark:text-red-400">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <span class="text-sm font-medium hidden sm:inline">错误</span>
          </div>
          <div v-else-if="nodes.length > 0" class="flex items-center space-x-3">
            <div class="flex items-center space-x-2 text-green-600 dark:text-green-400">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
              <span class="text-sm font-medium hidden sm:inline">{{ nodes.length }} 个节点</span>
            </div>

            <!-- 缓存状态指示 -->
            <div v-if="isFromCache" class="flex items-center space-x-1 text-orange-600 dark:text-orange-400">
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4"></path>
              </svg>
              <span class="text-xs hidden sm:inline">缓存</span>
            </div>

            <!-- 重新生成按钮 -->
            <button
              @click="regenerateAnalysis"
              :disabled="isLoading"
              class="flex items-center space-x-1 px-2 py-1 text-xs text-slate-600 dark:text-slate-400 hover:text-slate-800 dark:hover:text-slate-200 hover:bg-slate-100 dark:hover:bg-slate-700 rounded transition-colors disabled:opacity-50"
              title="重新生成分析"
            >
              <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
              </svg>
              <span class="hidden sm:inline">重新生成</span>
            </button>
          </div>

          <!-- 主题切换按钮 -->
          <ThemeToggle />
        </div>
      </div>

      <!-- 主内容区域 -->
      <div class="flex-1 overflow-hidden">
        <div v-if="isLoading" class="flex items-center justify-center h-full">
          <div class="text-center p-8">
            <div class="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-6"></div>
            <h3 class="text-lg font-semibold text-slate-800 dark:text-slate-200 mb-2">正在生成推演分析</h3>
            <p class="text-slate-600 dark:text-slate-400">AI正在深度分析您的主题，构建推演网络...</p>
          </div>
        </div>

        <div v-else-if="error" class="flex items-center justify-center h-full">
          <div class="text-center p-8">
            <div class="w-16 h-16 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center mx-auto mb-6">
              <svg class="w-8 h-8 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
            <h3 class="text-lg font-semibold text-red-800 dark:text-red-200 mb-2">分析失败</h3>
            <p class="text-red-600 dark:text-red-400">{{ error }}</p>
          </div>
        </div>

        <div v-else-if="nodes.length === 0" class="flex items-center justify-center h-full">
          <div class="text-center p-8">
            <div class="w-16 h-16 bg-slate-100 dark:bg-slate-700 rounded-full flex items-center justify-center mx-auto mb-6">
              <svg class="w-8 h-8 text-slate-500 dark:text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
              </svg>
            </div>
            <h3 class="text-lg font-semibold text-slate-800 dark:text-slate-200 mb-2">暂无推演节点</h3>
            <p class="text-slate-600 dark:text-slate-400">没有生成任何推演节点，请尝试更换主题或提示</p>
          </div>
        </div>

        <div v-else class="h-full p-2 sm:p-4">
          <div class="h-full border border-slate-200 dark:border-slate-700 rounded overflow-hidden bg-white dark:bg-slate-800">
            <DeductiveTreeGraph :nodes="nodes" :connections="edges" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* Add any specific styles for this page here */
</style>

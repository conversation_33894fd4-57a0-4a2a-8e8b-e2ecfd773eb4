@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    @apply border-slate-200 dark:border-slate-700;
  }
  
  body {
    @apply bg-white dark:bg-slate-900 text-slate-900 dark:text-slate-100 font-sans;
    margin: 0;
    padding: 0;
    min-height: 100vh;
    font-feature-settings: 'kern' 1, 'liga' 1, 'calt' 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    transition: background-color 0.3s ease, color 0.3s ease;
  }
  
  h1, h2, h3, h4, h5, h6 {
    @apply font-semibold;
    line-height: 1.2;
  }
  
  p {
    line-height: 1.6;
  }
}

@layer components {
  .btn-primary {
    @apply bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 text-white font-medium py-3 px-6 rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 active:scale-95;
  }
  
  .btn-secondary {
    @apply bg-white dark:bg-slate-800 hover:bg-slate-50 dark:hover:bg-slate-700 text-slate-700 dark:text-slate-300 font-medium py-3 px-6 rounded-xl border border-slate-200 dark:border-slate-600 transition-all duration-200 shadow-sm hover:shadow-lg transform hover:-translate-y-0.5 active:scale-95;
  }
  
  .card {
    @apply bg-white dark:bg-slate-800 rounded-2xl shadow-sm hover:shadow-lg transition-all duration-300 border border-slate-100 dark:border-slate-700 hover:border-slate-200 dark:hover:border-slate-600;
  }
  
  .input-field {
    @apply w-full px-4 py-3 rounded-xl border border-slate-200 dark:border-slate-600 focus:border-blue-500 dark:focus:border-blue-400 focus:ring-2 focus:ring-blue-200 dark:focus:ring-blue-800 transition-all duration-200 outline-none bg-white dark:bg-slate-800 text-slate-900 dark:text-slate-100;
  }
  
  .question-card {
    @apply card p-6 mb-4 hover:scale-[1.02] transition-all duration-300 hover:shadow-xl;
  }
  
  .category-badge {
    @apply inline-flex items-center px-3 py-1 rounded-full text-sm font-medium;
  }

  /* 系统化分析专用样式 */
  .analysis-slider {
    -webkit-appearance: none;
    appearance: none;
    background: transparent;
    cursor: pointer;
  }

  .analysis-slider::-webkit-slider-track {
    @apply bg-slate-200 dark:bg-slate-600 rounded-full h-2;
  }

  .analysis-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    @apply bg-blue-500 dark:bg-blue-600 rounded-full w-5 h-5 cursor-pointer;
    margin-top: -6px;
  }

  .analysis-slider::-moz-range-track {
    @apply bg-slate-200 dark:bg-slate-600 rounded-full h-2 border-0;
  }

  .analysis-slider::-moz-range-thumb {
    @apply bg-blue-500 dark:bg-blue-600 rounded-full w-5 h-5 cursor-pointer border-0;
    margin-top: -6px;
  }

  .analysis-slider:focus::-webkit-slider-thumb {
    @apply ring-2 ring-blue-500 ring-offset-2 dark:ring-offset-slate-800;
  }

  .analysis-slider:focus::-moz-range-thumb {
    @apply ring-2 ring-blue-500 ring-offset-2 dark:ring-offset-slate-800;
  }
}

@layer utilities {
  .text-gradient {
    @apply bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent;
  }
  
  .bg-gradient-primary {
    @apply bg-gradient-to-r from-blue-500 to-purple-500;
  }
}

/* 优化的彩虹动画 - 性能提升 */
@keyframes liquid-rainbow {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

@keyframes liquid-glow {
  0%, 100% { 
    background-position: 0% 50%;
    opacity: 0.4;
  }
  50% { 
    background-position: 100% 50%;
    opacity: 0.6;
  }
}

@keyframes liquid-glow-mid {
  0%, 100% { 
    background-position: 0% 50%;
    opacity: 0.3;
  }
  50% { 
    background-position: 100% 50%;
    opacity: 0.5;
  }
}

/* 玻璃质感动画 */
@keyframes glass-shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

@keyframes glass-float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-2px); }
}

.animate-liquid-rainbow {
  background-size: 200% 200%;
  animation: liquid-rainbow 4s ease-in-out infinite;
  will-change: background-position;
}

.animate-liquid-glow {
  background-size: 200% 200%;
  animation: liquid-glow 6s ease-in-out infinite;
  will-change: background-position, opacity;
}

.animate-liquid-glow-mid {
  background-size: 200% 200%;
  animation: liquid-glow-mid 5s ease-in-out infinite;
  will-change: background-position, opacity;
}

.animate-glass-shimmer {
  background-size: 200% 100%;
  animation: glass-shimmer 3s ease-in-out infinite;
  will-change: background-position;
}

.animate-glass-float {
  animation: glass-float 4s ease-in-out infinite;
  will-change: transform;
}

/* 自定义动画 */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { 
    opacity: 0; 
    transform: translateY(20px); 
  }
  to { 
    opacity: 1; 
    transform: translateY(0); 
  }
}

@keyframes slideDown {
  from { 
    opacity: 0; 
    transform: translateY(-10px); 
  }
  to { 
    opacity: 1; 
    transform: translateY(0); 
  }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0,0,0);
  }
  40%, 43% {
    transform: translate3d(0, -8px, 0);
  }
  70% {
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

.animate-fade-in {
  animation: fadeIn 0.6s ease-out;
}

.animate-slide-up {
  animation: slideUp 0.6s ease-out;
}

.animate-slide-down {
  animation: slideDown 0.3s ease-out;
}

.animate-pulse-slow {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: rgba(148, 163, 184, 0.3);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(148, 163, 184, 0.5);
}

/* 暗色模式滚动条 */
.dark ::-webkit-scrollbar-thumb {
  background: rgba(71, 85, 105, 0.3);
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: rgba(71, 85, 105, 0.5);
}

/* 选择文本样式 */
::selection {
  background: rgba(59, 130, 246, 0.1);
  color: rgb(59, 130, 246);
}

.dark ::selection {
  background: rgba(59, 130, 246, 0.2);
  color: rgb(147, 197, 253);
}

/* 焦点样式优化 */
button:focus-visible,
input:focus-visible {
  outline: 2px solid rgb(59, 130, 246);
  outline-offset: 2px;
}

.dark button:focus-visible,
.dark input:focus-visible {
  outline-color: rgb(147, 197, 253);
}

/* 加载动画优化 */
.loading-dots {
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.loading-dots > div {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: currentColor;
  animation: bounce 1.4s ease-in-out infinite both;
}

.loading-dots > div:nth-child(1) { animation-delay: -0.32s; }
.loading-dots > div:nth-child(2) { animation-delay: -0.16s; }
.loading-dots > div:nth-child(3) { animation-delay: 0s; }

/* 响应式优化 */
@media (max-width: 640px) {
  .question-card {
    @apply hover:scale-100;
  }
}


import { getActiveModelConfig } from './geminiApi'
import { AnalysisNode, NodeConnection } from './systemicThinking'

export interface DeductiveAnalysisResult {
  initialTopic: string;
  positiveBranch: {
    title: string;
    description: string;
    nodes: AnalysisNode[];
    connections: NodeConnection[];
  };
  negativeBranch: {
    title: string;
    description: string;
    nodes: AnalysisNode[];
    connections: NodeConnection[];
  };
}

export class LLMDeductiveAnalyzer {
  private activeModel: any;

  constructor() {
    this.activeModel = getActiveModelConfig();
  }

  async generateDeductiveAnalysis(topic: string, thinkingPrompt: string): Promise<DeductiveAnalysisResult> {
    const prompt = this.buildPrompt(topic, thinkingPrompt);

    try {
      if (this.activeModel?.apiKey) {
        const { text } = await this.callLLMAPI(prompt);
        const parsedResult = this.parseResponse(text);
        return parsedResult;
      } else {
        // Fallback to local generation if no API key
        return this.generateLocalDeductiveAnalysis(topic);
      }
    } catch (error) {
      console.error(`LLM Deductive Analysis failed, falling back to local generation:`, error);
      return this.generateLocalDeductiveAnalysis(topic);
    }
  }

  private buildPrompt(topic: string, thinkingPrompt: string): string {
    const promptAddition = thinkingPrompt ? `\n请特别注意以下思考提示："${thinkingPrompt}"` : '';

    return `请对主题"${topic}"进行推演分析，分别从"积极方向"和"负面方向"进行深入探讨。${promptAddition}\n\n请严格按照以下JSON格式返回，确保每个分支内的节点形成一个树形结构，从根节点（level 1）开始，逐层向下推演：\n\n{\n  "initialTopic": "${topic}",\n  "positiveBranch": {\n    "title": "积极方向的总结",\n    "description": "对积极方向的详细描述",\n    "nodes": [\n      {\n        "id": "pos-node-1",\n        "title": "积极节点1",\n        "description": "积极节点1的描述",\n        "level": 1,\n        "impact": "high",\n        "modelSource": "deductive-positive",\n        "type": "positive"\n      },\n      {\n        "id": "pos-node-2",\n        "title": "积极节点2",\n        "description": "积极节点2的描述",\n        "level": 2,\n        "impact": "medium",\n        "modelSource": "deductive-positive",\n        "type": "positive"\n      }\n    ],\n    "connections": [\n      {\n        "from": "pos-node-1",\n        "to": "pos-node-2",\n        "type": "strong",\n        "description": "连接描述",\n        "strength": 0.9\n      }\n    ]\n  },\n  "negativeBranch": {\n    "title": "负面方向的总结",\n    "description": "对负面方向的详细描述",\n    "nodes": [\n      {\n        "id": "neg-node-1",\n        "title": "负面节点1",\n        "description": "负面节点1的描述",\n        "level": 1,\n        "impact": "critical",\n        "modelSource": "deductive-negative",\n        "type": "negative"\n      },\n      {\n        "id": "neg-node-2",\n        "title": "负面节点2",\n        "description": "负面节点2的描述",\n        "level": 2,\n        "impact": "high",\n        "modelSource": "deductive-negative",\n        "type": "negative"\n      }\n    ],\n    "connections": [\n      {\n        "from": "neg-node-1",\n        "to": "neg-node-2",\n        "type": "weak",\n        "description": "连接描述",\n        "strength": 0.7\n      }\n    ]\n  }\n}\n\n注意：\n- 节点ID必须唯一，且在各自的分支内以"pos-"或"neg-"开头。\n- level从1开始，表示推演的层级，根节点为1。\n- modelSource请使用"deductive-positive"和"deductive-negative"。\n- type字段必须为"positive"或"negative"。\n- 确保连接的from和to对应存在的节点ID，并且连接应体现父子关系，形成树状结构。\n- 每个分支至少生成3个节点，并确保有足够的连接来形成至少两层结构。\n`;
  }

  private async callLLMAPI(prompt: string): Promise<{ text: string, tokenCount: number }> {
    // This will use the same LLM API call logic as systemicThinking.ts
    // For simplicity, I'm reusing the geminiApi.ts logic directly here.
    // In a larger application, you might abstract this further.
    const { GoogleGenerativeAI } = await import('@google/generative-ai');
    const genAI = new GoogleGenerativeAI(this.activeModel.apiKey);
    const geminiModel = genAI.getGenerativeModel({ model: "gemini-2.5-flash" });

    try {
      const result = await geminiModel.generateContent(prompt);
      const response = result.response;
      const text = response.text();
      const tokenCount = (response as any).usageMetadata?.totalTokenCount || 0;
      console.log(`Gemini Deductive API Response:`, text);
      return { text, tokenCount };
    } catch (error) {
      console.error(`Gemini Deductive API Error:`, error);
      throw error; // Re-throw to be caught by generateDeductiveAnalysis
    }
  }

  private parseResponse(text: string): DeductiveAnalysisResult {
    try {
      const cleanText = text.replace(/```json\n?/g, '').replace(/```\n?/g, '').trim();
      const parsed = JSON.parse(cleanText);
      return parsed;
    } catch (error) {
      console.error('解析LLM推演响应失败:', error, '原始文本:', text);
      throw new Error('LLM返回的推演数据格式不正确');
    }
  }

  // 新增：用于动态扩展的LLM调用
  async callLLMForExpansion(prompt: string): Promise<{ text: string, tokenCount: number }> {
    if (this.activeModel?.apiKey) {
      return await this.callLLMAPI(prompt);
    } else {
      throw new Error('没有可用的LLM API配置');
    }
  }

  // 新增：检查模型配置的公共方法
  getModelConfig() {
    return this.activeModel ? {
      id: this.activeModel.id,
      name: this.activeModel.name,
      provider: this.activeModel.provider,
      hasApiKey: !!this.activeModel.apiKey,
      isActive: this.activeModel.isActive
    } : null
  }

  // 新增：检查是否有有效配置
  hasValidConfig(): boolean {
    return !!(this.activeModel?.apiKey)
  }

  private generateLocalDeductiveAnalysis(topic: string): DeductiveAnalysisResult {
    // 基于主题生成更智能的本地分析，确保至少6个节点

    // 生成更丰富的积极方向节点网络（至少3个积极节点）
    const positiveNodes: AnalysisNode[] = [
      // 第一层积极节点
      {
        id: 'pos-1',
        title: `${topic}的核心机遇`,
        description: `深入分析${topic}可能带来的核心机遇和战略发展空间，包括市场潜力、技术优势和资源整合机会`,
        level: 1,
        impact: 'high',
        modelSource: 'deductive-positive',
        type: 'positive',
        insights: [
          `${topic}具有巨大的发展潜力`,
          '市场需求持续增长',
          '技术条件日趋成熟'
        ]
      },
      {
        id: 'pos-2',
        title: '创新突破点',
        description: `识别${topic}领域中的关键创新突破点，分析技术革新、模式创新和应用场景拓展的可能性`,
        level: 1,
        impact: 'high',
        modelSource: 'deductive-positive',
        type: 'positive',
        insights: [
          '技术创新将带来颠覆性变化',
          '新的商业模式正在涌现',
          '跨领域融合创造新机遇'
        ]
      },
      {
        id: 'pos-3',
        title: '资源整合优势',
        description: `评估可以利用的现有资源和优势条件，包括人才储备、技术积累、资金支持和政策环境`,
        level: 2,
        impact: 'medium',
        modelSource: 'deductive-positive',
        type: 'positive',
        insights: [
          '现有资源基础扎实',
          '人才团队经验丰富',
          '政策环境日益优化'
        ]
      },
      {
        id: 'pos-4',
        title: '长期价值创造',
        description: `预测${topic}的长期积极影响和价值创造潜力，包括经济效益、社会价值和生态效应`,
        level: 2,
        impact: 'high',
        modelSource: 'deductive-positive',
        type: 'positive',
        insights: [
          '长期投资回报可观',
          '社会价值持续放大',
          '生态效应逐步显现'
        ]
      },
      {
        id: 'pos-5',
        title: '技术赋能效应',
        description: `分析技术进步对${topic}的赋能作用，包括效率提升、成本降低和体验优化等方面`,
        level: 2,
        impact: 'high',
        modelSource: 'deductive-positive',
        type: 'positive',
        insights: [
          '技术赋能效果显著',
          '运营效率大幅提升',
          '用户体验持续改善'
        ]
      },
      {
        id: 'pos-6',
        title: '生态协同价值',
        description: `探索${topic}在更大生态系统中的协同价值，包括产业链整合、平台效应和网络价值`,
        level: 2,
        impact: 'medium',
        modelSource: 'deductive-positive',
        type: 'positive',
        insights: [
          '生态协同效应明显',
          '产业链价值不断放大',
          '网络效应持续增强'
        ]
      },
      {
        id: 'pos-7',
        title: '经济增长驱动',
        description: `分析${topic}对经济增长的积极推动作用，包括GDP贡献、产业升级和就业创造等宏观经济效应`,
        level: 3,
        impact: 'high',
        modelSource: 'deductive-positive',
        type: 'positive',
        insights: [
          'GDP贡献持续增长',
          '产业结构优化升级',
          '经济发展质量提升'
        ]
      },
      {
        id: 'pos-8',
        title: '社会就业促进',
        description: `评估${topic}对就业市场的积极影响，包括新岗位创造、技能提升和职业发展机会`,
        level: 3,
        impact: 'medium',
        modelSource: 'deductive-positive',
        type: 'positive',
        insights: [
          '新兴岗位大量涌现',
          '技能培训需求增加',
          '职业发展路径拓宽'
        ]
      }
    ];

    // 生成更丰富的负面方向节点网络（至少3个负面节点）
    const negativeNodes: AnalysisNode[] = [
      // 第一层负面节点
      {
        id: 'neg-1',
        title: `${topic}的核心风险`,
        description: `深入分析${topic}可能面临的核心风险和关键挑战，包括技术风险、市场风险、政策风险和竞争风险`,
        level: 1,
        impact: 'critical',
        modelSource: 'deductive-negative',
        type: 'negative',
        insights: [
          `${topic}存在不确定性因素`,
          '市场竞争日趋激烈',
          '技术路径存在分歧'
        ]
      },
      {
        id: 'neg-2',
        title: '实施复杂性挑战',
        description: `识别${topic}实施过程中的复杂性挑战，包括技术难度、资源协调、时间压力和质量控制等方面`,
        level: 1,
        impact: 'high',
        modelSource: 'deductive-negative',
        type: 'negative',
        insights: [
          '实施难度超出预期',
          '资源协调存在困难',
          '时间窗口相对紧迫'
        ]
      },
      {
        id: 'neg-3',
        title: '资源约束瓶颈',
        description: `评估可能遇到的资源约束和瓶颈问题，包括资金限制、人才短缺、技术壁垒和基础设施不足`,
        level: 2,
        impact: 'high',
        modelSource: 'deductive-negative',
        type: 'negative',
        insights: [
          '资金需求量巨大',
          '专业人才相对稀缺',
          '基础设施有待完善'
        ]
      },
      {
        id: 'neg-4',
        title: '系统性风险隐患',
        description: `分析${topic}可能引发的系统性风险和连锁反应，包括技术依赖、单点故障和级联效应`,
        level: 2,
        impact: 'critical',
        modelSource: 'deductive-negative',
        type: 'negative',
        insights: [
          '系统复杂性带来脆弱性',
          '关键节点存在单点风险',
          '故障可能产生连锁反应'
        ]
      },
      {
        id: 'neg-5',
        title: '技术演进不确定性',
        description: `探讨技术发展路径的不确定性和潜在风险，包括技术选择、标准制定和兼容性问题`,
        level: 2,
        impact: 'high',
        modelSource: 'deductive-negative',
        type: 'negative',
        insights: [
          '技术路线存在分歧',
          '标准化进程缓慢',
          '兼容性问题复杂'
        ]
      },
      {
        id: 'neg-6',
        title: '社会接受度挑战',
        description: `分析来自社会各界的质疑和阻力，包括公众认知、利益冲突和文化适应性问题`,
        level: 2,
        impact: 'medium',
        modelSource: 'deductive-negative',
        type: 'negative',
        insights: [
          '公众认知有待提升',
          '利益相关方存在分歧',
          '文化适应需要时间'
        ]
      },
      {
        id: 'neg-7',
        title: '经济损失风险',
        description: `评估${topic}可能带来的经济损失和财务风险，包括投资回报不确定性、成本超支和市场失败风险`,
        level: 3,
        impact: 'critical',
        modelSource: 'deductive-negative',
        type: 'negative',
        insights: [
          '投资回报存在不确定性',
          '项目成本可能超支',
          '市场接受度有待验证'
        ]
      },
      {
        id: 'neg-8',
        title: '伦理道德争议',
        description: `探讨${topic}可能引发的伦理道德争议和社会责任问题，包括隐私保护、公平性和可持续性考量`,
        level: 3,
        impact: 'medium',
        modelSource: 'deductive-negative',
        type: 'negative',
        insights: [
          '隐私保护面临挑战',
          '公平性问题需要关注',
          '可持续发展存在疑虑'
        ]
      }
    ];

    // 创建更复杂的积极方向连接网络
    const positiveConnections: NodeConnection[] = [
      // 第一层到第二层的连接
      { from: 'pos-1', to: 'pos-3', type: 'strong', description: '机遇带来资源优势' , strength: 0.9},
      { from: 'pos-1', to: 'pos-4', type: 'strong', description: '机遇产生长期收益' , strength: 0.8},
      { from: 'pos-2', to: 'pos-5', type: 'strong', description: '创新推动技术突破' , strength: 0.9},
      { from: 'pos-2', to: 'pos-6', type: 'strong', description: '创新带来社会效益' , strength: 0.7},

      // 第二层到第三层的连接
      { from: 'pos-4', to: 'pos-7', type: 'strong', description: '长期收益促进经济增长' , strength: 0.8},
      { from: 'pos-5', to: 'pos-7', type: 'strong', description: '技术突破推动经济增长' , strength: 0.7},
      { from: 'pos-6', to: 'pos-8', type: 'strong', description: '社会效益创造就业机会' , strength: 0.6},

      // 跨层级的关联连接
      { from: 'pos-1', to: 'pos-7', type: 'weak', description: '机遇直接影响经济' , strength: 0.5},
      { from: 'pos-3', to: 'pos-8', type: 'weak', description: '资源优势促进就业' , strength: 0.4},
    ];

    // 创建更复杂的负面方向连接网络
    const negativeConnections: NodeConnection[] = [
      // 第一层到第二层的连接
      { from: 'neg-1', to: 'neg-3', type: 'strong', description: '风险导致资源限制' , strength: 0.9},
      { from: 'neg-1', to: 'neg-4', type: 'strong', description: '风险产生负面后果' , strength: 0.8},
      { from: 'neg-2', to: 'neg-5', type: 'strong', description: '实施障碍带来技术风险' , strength: 0.8},
      { from: 'neg-2', to: 'neg-6', type: 'strong', description: '实施障碍引发社会阻力' , strength: 0.7},

      // 第二层到第三层的连接
      { from: 'neg-4', to: 'neg-7', type: 'strong', description: '负面后果造成经济损失' , strength: 0.9},
      { from: 'neg-5', to: 'neg-7', type: 'strong', description: '技术风险导致经济损失' , strength: 0.7},
      { from: 'neg-6', to: 'neg-8', type: 'strong', description: '社会阻力引发道德争议' , strength: 0.6},

      // 跨层级的关联连接
      { from: 'neg-1', to: 'neg-7', type: 'weak', description: '风险直接影响经济' , strength: 0.6},
      { from: 'neg-3', to: 'neg-8', type: 'weak', description: '资源限制引发争议' , strength: 0.4},
    ];

    return {
      initialTopic: topic,
      positiveBranch: {
        title: '积极方向',
        description: '对主题的积极推演',
        nodes: positiveNodes,
        connections: positiveConnections,
      },
      negativeBranch: {
        title: '负面方向',
        description: '对主题的负面推演',
        nodes: negativeNodes,
        connections: negativeConnections,
      },
    };
  }
}
